{"name": "portavio", "version": "0.4.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "kysely": "^0.28.2", "lucide-react": "^0.511.0", "motion": "^12.12.1", "next": "15.3.2", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "pg": "^8.16.0", "postcss": "^8.5.3", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "zod": "^3.25.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5"}}