"use client";

import { AddTransactionModal } from "@/components/transactions/add-transaction-modal";
import { TransactionsDataTable } from "@/components/transactions/transactions-data-table";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { ArrowLeft, PieChart } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface PortfolioDetailClientProps {
  portfolio: Portfolio;
  className?: string;
}

export function PortfolioDetailClient({
  portfolio,
  className,
}: PortfolioDetailClientProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAddTransaction = () => {
    setIsModalOpen(true);
  };

  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);
  };

  const handleModalSuccess = () => {
    setIsModalOpen(false);
    // TanStack Query will automatically refetch data
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/portfolios">Portofolii</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{portfolio.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-start gap-12">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Înapoi
            </Button>
            <div>
              <div className="flex items-center gap-3">
                <PieChart className="h-8 w-8 text-portavio-blue" />
                <h1 className="text-3xl font-bold text-foreground">
                  {portfolio.name}
                </h1>
                <Badge variant="secondary" className="text-xs">
                  {portfolio.is_active ? "Activ" : "Inactiv"}
                </Badge>
              </div>
              {portfolio.description && (
                <p className="text-muted-foreground mt-2">
                  {portfolio.description}
                </p>
              )}
            </div>
          </div>
        </div>

        <TransactionsDataTable
          portfolioId={portfolio.id}
          portfolioName={portfolio.name}
          onAddTransaction={handleAddTransaction}
        />
      </div>

      {/* Add Transaction Modal */}
      <AddTransactionModal
        open={isModalOpen}
        onOpenChange={handleModalOpenChange}
        onSuccess={handleModalSuccess}
        defaultPortfolioId={portfolio.id}
      />
    </div>
  );
}
