"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Portfolio } from "@/utils/db/portfolio-queries";
import { formatDate } from "date-fns";
import { ro } from "date-fns/locale/ro";
import {
  AlertCircle,
  CheckCircle,
  Loader2,
  PieChart,
  RotateCcw,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { toast } from "sonner";

interface InactivePortfoliosSectionProps {
  inactivePortfolios: Portfolio[];
  onPortfolioReactivated?: () => void;
  isLoading?: boolean;
}

export function InactivePortfoliosSection({
  inactivePortfolios,
  onPortfolioReactivated,
  isLoading = false,
}: InactivePortfoliosSectionProps) {
  const [reactivatingIds, setReactivatingIds] = useState<Set<string>>(
    new Set()
  );

  const handleReactivatePortfolio = async (portfolioId: string) => {
    try {
      setReactivatingIds((prev) => new Set(prev).add(portfolioId));

      const response = await fetch(
        `/api/portfolios/${portfolioId}/reactivate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nu s-a putut reactiva portofoliul");
      }

      const result = await response.json();
      toast.success(
        result.message || "Portofoliul a fost reactivat cu succes!"
      );

      if (onPortfolioReactivated) {
        onPortfolioReactivated();
      }
    } catch (error) {
      console.error("Error reactivating portfolio:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "A apărut o eroare la reactivarea portofoliului";
      toast.error(errorMessage);
    } finally {
      setReactivatingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(portfolioId);
        return newSet;
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div className="flex items-start gap-3">
            <Loader2 className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0 animate-spin" />
            <div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                Se încarcă portofoliile inactive...
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Vă rugăm să așteptați.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (inactivePortfolios.length === 0) {
    return (
      <div className="space-y-6">
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                Toate portofoliile sunt active
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">
                Nu ai portofolii dezactivate în acest moment. Toate portofoliile
                tale sunt active și vizibile în lista principală.
              </p>
              <Button asChild className="mt-4 w-full" variant={"outline"}>
                <Link href="/portfolios">Vezi portofoliile</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
        <div className="flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
              Portofolii inactive
            </p>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Aceste portofolii sunt ascunse din lista principală. Poți să le
              reactivezi oricând pentru a le face din nou vizibile.
            </p>
          </div>
        </div>
      </div>

      <div className="grid gap-4">
        {inactivePortfolios.map((portfolio) => {
          const isReactivating = reactivatingIds.has(portfolio.id);

          return (
            <Card key={portfolio.id} className="border-l-4 border-l-orange-500">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between gap-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <PieChart className="h-5 w-5 text-muted-foreground" />
                      <CardTitle className="text-lg">
                        {portfolio.name}
                      </CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        Inactiv
                      </Badge>
                    </div>
                    {portfolio.description && (
                      <CardDescription className="text-sm">
                        {portfolio.description}
                      </CardDescription>
                    )}
                  </div>
                  <Button
                    onClick={() => handleReactivatePortfolio(portfolio.id)}
                    disabled={isReactivating}
                    size="sm"
                    className="gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700"
                  >
                    {isReactivating ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Se reactivează...
                      </>
                    ) : (
                      <>
                        <RotateCcw className="h-4 w-4" />
                        Reactivează
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>
                    Creat:{" "}
                    {formatDate(portfolio.created_at, "PPpp", { locale: ro })}
                  </span>
                  <span>
                    Ultima actualizare:{" "}
                    {formatDate(portfolio.updated_at, "PPpp", { locale: ro })}
                  </span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
