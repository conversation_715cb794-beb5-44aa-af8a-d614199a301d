"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { useDeleteTransaction } from "@/hooks/use-transactions-query";
import {
  formatQuantity,
  formatTransactionDate,
} from "@/lib/transaction-schemas";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";
import { AlertTriangle, Loader2, TrendingDown, TrendingUp } from "lucide-react";
import { toast } from "sonner";

interface DeleteTransactionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: TransactionWithAssetInfo;
  onSuccess?: () => void;
}

export function DeleteTransactionDialog({
  open,
  onOpenChange,
  transaction,
  onSuccess,
}: DeleteTransactionDialogProps) {
  // Use TanStack Query mutation for deleting transactions
  const deleteTransactionMutation = useDeleteTransaction();

  const handleDelete = async () => {
    deleteTransactionMutation.mutate(transaction.id, {
      onSuccess: () => {
        toast.success("Tranzacția a fost ștearsă cu succes!");
        onOpenChange(false);
        onSuccess?.();
      },
      onError: (error) => {
        const errorMessage =
          error instanceof Error ? error.message : "A apărut o eroare";
        toast.error(errorMessage);
      },
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !deleteTransactionMutation.isPending) {
      onOpenChange(newOpen);
    }
  };

  const formatPrice = (price: number) => {
    return price.toLocaleString("ro-RO", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Șterge tranzacția
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p>
                Ești sigur că vrei să ștergi această tranzacție? Această acțiune
                nu poate fi anulată.
              </p>

              {/* Transaction Details */}
              <div className="bg-muted rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-lg">
                      {transaction.ticker}
                    </span>
                    <Badge
                      variant={
                        transaction.transaction_type === "BUY"
                          ? "default"
                          : "destructive"
                      }
                      className="text-xs text-white"
                    >
                      {transaction.transaction_type === "BUY" ? (
                        <>
                          <TrendingUp className="h-3 w-3 mr-1" />
                          BUY
                        </>
                      ) : (
                        <>
                          <TrendingDown className="h-3 w-3 mr-1" />
                          SELL
                        </>
                      )}
                    </Badge>
                  </div>
                </div>

                {transaction.asset && (
                  <p className="text-sm text-muted-foreground">
                    {transaction.asset.name}
                  </p>
                )}

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <p className="text-muted-foreground">Cantitate</p>
                    <p className="font-medium">
                      {formatQuantity(transaction.quantity)}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Data</p>
                    <p className="font-medium">
                      {formatTransactionDate(transaction.transaction_date)}
                    </p>
                  </div>
                  {transaction.price && (
                    <>
                      <div>
                        <p className="text-muted-foreground">Preț tranzacție</p>
                        <p className="font-medium">
                          {formatPrice(transaction.price)}
                        </p>
                      </div>
                    </>
                  )}
                  {transaction.transaction_fee !== undefined &&
                    transaction.transaction_fee !== null && (
                      <div>
                        <p className="text-muted-foreground">Comision</p>
                        <p className="font-medium">
                          {formatPrice(transaction.transaction_fee)}
                        </p>
                      </div>
                    )}
                </div>

                {transaction.notes && (
                  <div className="pt-2 border-t">
                    <p className="text-muted-foreground text-xs">Note</p>
                    <p className="text-sm">{transaction.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteTransactionMutation.isPending}>
            Anulează
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteTransactionMutation.isPending}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {deleteTransactionMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Se șterge...
              </>
            ) : (
              "Șterge tranzacția"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
