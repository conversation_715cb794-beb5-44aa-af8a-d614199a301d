"use client";

import { Plus, Receipt } from "lucide-react";
import { useCallback, useMemo, useState } from "react";

import { DeleteTransactionDialog } from "@/components/transactions/delete-transaction-dialog";
import { EditTransactionModal } from "@/components/transactions/edit-transaction-modal";
import { createTransactionColumns } from "@/components/transactions/transactions-table-columns";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { useTransactions } from "@/hooks/use-transactions-query";
import { cn } from "@/lib/utils";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";

interface TransactionsDataTableProps {
  portfolioId: string;
  portfolioName: string;
  onAddTransaction?: () => void;
  className?: string;
  pageSize?: number;
}

export function TransactionsDataTable({
  portfolioId,
  portfolioName,
  onAddTransaction,
  className,
}: TransactionsDataTableProps) {
  const [editingTransaction, setEditingTransaction] =
    useState<TransactionWithAssetInfo | null>(null);
  const [deletingTransaction, setDeletingTransaction] =
    useState<TransactionWithAssetInfo | null>(null);

  const {
    data: transactionsData,
    isLoading,
    error,
    refetch,
  } = useTransactions({
    portfolioId,
  });

  const handleEditTransaction = useCallback(
    (transaction: TransactionWithAssetInfo) => {
      setEditingTransaction(transaction);
    },
    []
  );

  const handleDeleteTransaction = useCallback(
    (transaction: TransactionWithAssetInfo) => {
      setDeletingTransaction(transaction);
    },
    []
  );

  const handleEditModalClose = useCallback(() => {
    setEditingTransaction(null);
  }, []);

  const handleDeleteModalClose = useCallback(() => {
    setDeletingTransaction(null);
  }, []);

  const handleTransactionUpdated = useCallback(() => {
    setEditingTransaction(null);
  }, []);

  const handleTransactionDeleted = useCallback(() => {
    setDeletingTransaction(null);
  }, []);

  const handleRetryLoad = useCallback(() => {
    refetch();
  }, [refetch]);

  const columns = useMemo(
    () =>
      createTransactionColumns(handleEditTransaction, handleDeleteTransaction),
    [handleEditTransaction, handleDeleteTransaction]
  );

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-6 w-6 text-portavio-orange" />
              <CardTitle className="text-xl">Tranzacții</CardTitle>
            </div>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă tranzacție
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="rounded-full bg-red-100 p-4 mb-4">
              <Receipt className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold mb-2">
              Eroare la încărcarea tranzacțiilor
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              {error instanceof Error
                ? error.message
                : "A apărut o eroare neașteptată"}
            </p>
            <Button onClick={handleRetryLoad} variant="outline">
              Încearcă din nou
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (
    !isLoading &&
    (!transactionsData?.transactions ||
      transactionsData.transactions.length === 0)
  ) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-6 w-6 text-portavio-orange" />
              <CardTitle className="text-xl">Tranzacții</CardTitle>
            </div>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă tranzacție
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="rounded-full bg-muted p-4 mb-4">
              <Receipt className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Nicio tranzacție</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              Nu există încă tranzacții în portofoliul &quot;{portfolioName}
              &quot;. Adaugă prima ta tranzacție pentru a începe urmărirea
              investițiilor.
            </p>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă prima tranzacție
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-6 w-6 text-portavio-orange" />
              <div>
                <CardTitle className="text-xl flex items-center gap-2">
                  Tranzacții
                  <p
                    className="text-lg text-muted-foreground"
                    title="Numărul total de tranzacții (Buy + Sell)"
                  >
                    ({transactionsData?.totalCount || 0})
                  </p>
                </CardTitle>
              </div>
            </div>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă tranzacție
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="px-6 pb-6">
            <DataTable
              columns={columns}
              data={transactionsData?.transactions || []}
              searchPlaceholder="Caută tranzacții..."
              isLoading={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {editingTransaction && (
        <EditTransactionModal
          open={!!editingTransaction}
          onOpenChange={handleEditModalClose}
          transaction={editingTransaction}
          onSuccess={handleTransactionUpdated}
        />
      )}

      {deletingTransaction && (
        <DeleteTransactionDialog
          open={!!deletingTransaction}
          onOpenChange={handleDeleteModalClose}
          transaction={deletingTransaction}
          onSuccess={handleTransactionDeleted}
        />
      )}
    </>
  );
}
