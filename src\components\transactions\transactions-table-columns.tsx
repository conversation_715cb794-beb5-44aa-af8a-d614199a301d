"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Building2, Edit, MoreVertical, Trash2 } from "lucide-react";
import Image from "next/image";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  formatQuantity,
  formatTransactionDate,
} from "@/lib/transaction-schemas";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";

interface TransactionActionsProps {
  transaction: TransactionWithAssetInfo;
  onEdit: (transaction: TransactionWithAssetInfo) => void;
  onDelete: (transaction: TransactionWithAssetInfo) => void;
}

function TransactionActions({
  transaction,
  onEdit,
  onDelete,
}: TransactionActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Deschide meniul</span>
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onEdit(transaction)}>
          <Edit className="mr-2 h-4 w-4 dark:text-white" />
          Editează
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onDelete(transaction)}
          variant="destructive"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Șterge
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function createTransactionColumns(
  onEdit: (transaction: TransactionWithAssetInfo) => void,
  onDelete: (transaction: TransactionWithAssetInfo) => void
): ColumnDef<TransactionWithAssetInfo>[] {
  const formatPrice = (price: number) => {
    return price.toLocaleString("ro-RO", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  return [
    {
      accessorKey: "ticker",
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="Activ"
          isFirstColumn={true}
        />
      ),
      cell: ({ row }) => {
        const transaction = row.original;
        return (
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-md bg-muted">
              {transaction.asset?.logo_url ? (
                <Image
                  src={transaction.asset.logo_url}
                  alt={transaction.ticker}
                  className="h-6 w-6"
                  width={24}
                  height={24}
                />
              ) : (
                <Building2 className="h-6 w-6 text-muted-foreground" />
              )}
            </div>
            <div>
              <div className="font-semibold text-left">
                {transaction.ticker}
              </div>
              {transaction.asset && (
                <div className="text-sm text-muted-foreground">
                  {transaction.asset.name}
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "transaction_type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tip" />
      ),
      cell: ({ row }) => {
        const type = row.getValue("transaction_type") as string;
        return (
          <Badge
            variant={type === "BUY" ? "default" : "destructive"}
            className="font-medium text-white"
          >
            {type}
          </Badge>
        );
      },
      enableSorting: true,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "quantity",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Cantitate" />
        </div>
      ),
      cell: ({ row }) => {
        const quantity = row.getValue("quantity") as number;
        return (
          <div className="text-right font-medium">
            {formatQuantity(quantity)}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "price",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Preț tranzacție" />
        </div>
      ),
      cell: ({ row }) => {
        const price = row.getValue("price") as number;
        return (
          <div className="text-right font-medium">
            {price ? formatPrice(price) : "N/A"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "transaction_fee",
      header: ({ column }) => (
        <div className="text-right">
          <DataTableColumnHeader column={column} title="Comision" />
        </div>
      ),
      cell: ({ row }) => {
        const fee = row.getValue("transaction_fee") as number | undefined;
        return (
          <div className="text-right font-medium">
            {fee !== undefined && fee !== null ? formatPrice(fee) : "N/A"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "transaction_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Data" />
      ),
      cell: ({ row }) => {
        const transaction = row.original;
        return (
          <div>
            <div className="font-medium">
              {formatTransactionDate(transaction.transaction_date)}
            </div>
            {transaction.notes && (
              <div
                className="text-sm text-muted-foreground mt-1"
                title={transaction.notes}
              >
                {transaction.notes.length > 30
                  ? `${transaction.notes.substring(0, 30)}...`
                  : transaction.notes}
              </div>
            )}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      header: () => <div className="text-center">Acțiuni</div>,
      cell: ({ row }) => {
        const transaction = row.original;
        return (
          <div className="text-center">
            <TransactionActions
              transaction={transaction}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ];
}
