"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { PortfolioWithMetrics } from "@/utils/db/portfolio-queries";

export const portfoliosKeys = {
  all: ["portfolios"] as const,
  lists: () => [...portfoliosKeys.all, "list"] as const,
  list: (filters: string) => [...portfoliosKeys.lists(), { filters }] as const,
  details: () => [...portfoliosKeys.all, "detail"] as const,
  detail: (id: string) => [...portfoliosKeys.details(), id] as const,
  metrics: () => [...portfoliosKeys.all, "metrics"] as const,
};

async function fetchPortfoliosWithMetrics(): Promise<{
  portfolios: PortfolioWithMetrics[];
  count: number;
}> {
  const response = await fetch("/api/portfolios/metrics", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Nu s-au putut încărca portofoliile");
  }

  return response.json();
}

async function deletePortfolio(portfolioId: string): Promise<void> {
  const response = await fetch(`/api/portfolios/${portfolioId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Nu s-a putut șterge portofoliul");
  }
}

export function usePortfoliosWithMetrics() {
  return useQuery({
    queryKey: portfoliosKeys.metrics(),
    queryFn: fetchPortfoliosWithMetrics,
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
  });
}

export function useDeletePortfolio() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["deletePortfolio"],
    mutationFn: deletePortfolio,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: portfoliosKeys.all,
      });
    },
  });
}
